import{_ as o}from"./AdminLayout-964868e9.js";import i from"./DeleteUserForm-08cda7eb.js";import m from"./UpdatePasswordForm-2b1c806c.js";import r from"./UpdateProfileInformationForm-6f6199ae.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-53ab0fb8.js";import"./DangerButton-c07297f5.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-46c455e3.js";import"./InputLabel-686b32c8.js";import"./Modal-ba68642b.js";/* empty css                                                              */import"./SecondaryButton-11a8311f.js";import"./TextInput-c743e100.js";import"./PrimaryButton-170c8c15.js";import"./TextArea-2931743c.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
