import{T as h,o as n,c as u,a as o,u as s,w as y,F as _,Z as U,b as t,h as S,k as p,v as f,d as b,y as w,g as A,t as v,p as $,m as P}from"./app-53ab0fb8.js";import{_ as j,b as C}from"./AdminLayout-964868e9.js";import{_ as d}from"./InputError-46c455e3.js";import{_ as r}from"./InputLabel-686b32c8.js";import{P as L}from"./PrimaryButton-170c8c15.js";import{_ as i}from"./TextInput-c743e100.js";import{_ as q}from"./_plugin-vue_export-helper-c27b6911.js";const m=c=>($("data-v-d84acd2a"),c=c(),P(),c),B={class:"animate-top"},I={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},N=m(()=>t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900 mb-10"},"Add New Prospects",-1)),O=["onSubmit"],R={class:""},T=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Personal Information",-1)),D={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},F={class:""},E=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Company Information",-1)),M={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},W={class:""},Z=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Source & URLs",-1)),z={class:"grid grid-cols-1 md:grid-cols-6 gap-4"},G={class:"md:col-span-2"},H=["value"],J={class:"md:col-span-2"},K={class:"md:col-span-2"},Q={class:"md:col-span-3"},X={class:"md:col-span-3"},Y={class:""},ee=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Status & Priority",-1)),se={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},le=["value"],oe=["value"],te={class:""},ae=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Project & Budget Information",-1)),de={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},re=m(()=>t("option",{value:""},"Select budget range",-1)),ie=["value"],ne=m(()=>t("option",{value:""},"Select user",-1)),ue=["value"],me={class:"md:col-span-4"},ce={class:""},pe=m(()=>t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Conversation & Notes",-1)),ge={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},_e={class:"flex mt-6 items-center justify-between"},fe={class:"ml-auto flex items-center justify-end gap-x-6"},be=m(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1)),ve={__name:"Add",props:{users:Array,statusOptions:Array,priorityOptions:Array,leadSourceOptions:Array,budgetRangeOptions:Array},setup(c){const e=h({first_name:"",last_name:"",email:"",phone:"",company:"",position:"",country:"",city:"",lead_source:"other",lead_source_details:"",linkedin_url:"",website_url:"",company_website:"",status:"new",priority:"medium",score:0,initial_conversation:"",notes:"",next_follow_up_at:"",estimated_budget:"",project_type:"",requirements:"",budget_range:"",assigned_to:""}),V=()=>{e.post(route("prospects.store"))},k=g=>g.charAt(0).toUpperCase()+g.slice(1).replace("_"," "),x=g=>({under_1k:"Under $1,000","1k_5k":"$1,000 - $5,000","5k_10k":"$5,000 - $10,000","10k_25k":"$10,000 - $25,000","25k_50k":"$25,000 - $50,000",over_50k:"Over $50,000"})[g]||g;return(g,a)=>(n(),u(_,null,[o(s(U),{title:"Add Prospect"}),o(j,null,{default:y(()=>[t("div",B,[t("div",I,[N,t("form",{onSubmit:S(V,["prevent"]),class:"space-y-6"},[t("div",R,[T,t("div",D,[t("div",null,[o(r,{for:"first_name",value:"First Name *"}),o(i,{id:"first_name",modelValue:s(e).first_name,"onUpdate:modelValue":a[0]||(a[0]=l=>s(e).first_name=l),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.first_name},null,8,["message"])]),t("div",null,[o(r,{for:"last_name",value:"Last Name *"}),o(i,{id:"last_name",modelValue:s(e).last_name,"onUpdate:modelValue":a[1]||(a[1]=l=>s(e).last_name=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.last_name},null,8,["message"])]),t("div",null,[o(r,{for:"email",value:"Email *"}),o(i,{id:"email",modelValue:s(e).email,"onUpdate:modelValue":a[2]||(a[2]=l=>s(e).email=l),type:"email",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.email},null,8,["message"])]),t("div",null,[o(r,{for:"phone",value:"Phone"}),o(i,{id:"phone",modelValue:s(e).phone,"onUpdate:modelValue":a[3]||(a[3]=l=>s(e).phone=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.phone},null,8,["message"])])])]),t("div",F,[E,t("div",M,[t("div",null,[o(r,{for:"company",value:"Company"}),o(i,{id:"company",modelValue:s(e).company,"onUpdate:modelValue":a[4]||(a[4]=l=>s(e).company=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.company},null,8,["message"])]),t("div",null,[o(r,{for:"position",value:"Position"}),o(i,{id:"position",modelValue:s(e).position,"onUpdate:modelValue":a[5]||(a[5]=l=>s(e).position=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.position},null,8,["message"])]),t("div",null,[o(r,{for:"country",value:"Country"}),o(i,{id:"country",modelValue:s(e).country,"onUpdate:modelValue":a[6]||(a[6]=l=>s(e).country=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.country},null,8,["message"])]),t("div",null,[o(r,{for:"city",value:"City"}),o(i,{id:"city",modelValue:s(e).city,"onUpdate:modelValue":a[7]||(a[7]=l=>s(e).city=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.city},null,8,["message"])])])]),t("div",W,[Z,t("div",z,[t("div",G,[o(r,{for:"lead_source",value:"Lead Source *"}),p(t("select",{id:"lead_source","onUpdate:modelValue":a[8]||(a[8]=l=>s(e).lead_source=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(n(!0),u(_,null,b(c.leadSourceOptions,l=>(n(),u("option",{key:l,value:l},v(k(l)),9,H))),128))],512),[[f,s(e).lead_source]]),o(d,{class:"mt-2",message:s(e).errors.lead_source},null,8,["message"])]),t("div",J,[o(r,{for:"lead_source_details",value:"Lead Source Details"}),o(i,{id:"lead_source_details",modelValue:s(e).lead_source_details,"onUpdate:modelValue":a[9]||(a[9]=l=>s(e).lead_source_details=l),type:"text",class:"mt-1 block w-full",placeholder:"Additional details about the source"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.lead_source_details},null,8,["message"])]),t("div",K,[o(r,{for:"linkedin_url",value:"LinkedIn URL"}),o(i,{id:"linkedin_url",modelValue:s(e).linkedin_url,"onUpdate:modelValue":a[10]||(a[10]=l=>s(e).linkedin_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.linkedin_url},null,8,["message"])]),t("div",Q,[o(r,{for:"website_url",value:"Personal Website"}),o(i,{id:"website_url",modelValue:s(e).website_url,"onUpdate:modelValue":a[11]||(a[11]=l=>s(e).website_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.website_url},null,8,["message"])]),t("div",X,[o(r,{for:"company_website",value:"Company Website"}),o(i,{id:"company_website",modelValue:s(e).company_website,"onUpdate:modelValue":a[12]||(a[12]=l=>s(e).company_website=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.company_website},null,8,["message"])])])]),t("div",Y,[ee,t("div",se,[t("div",null,[o(r,{for:"status",value:"Status *"}),p(t("select",{id:"status","onUpdate:modelValue":a[13]||(a[13]=l=>s(e).status=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(n(!0),u(_,null,b(c.statusOptions,l=>(n(),u("option",{key:l,value:l},v(l.charAt(0).toUpperCase()+l.slice(1)),9,le))),128))],512),[[f,s(e).status]]),o(d,{class:"mt-2",message:s(e).errors.status},null,8,["message"])]),t("div",null,[o(r,{for:"priority",value:"Priority *"}),p(t("select",{id:"priority","onUpdate:modelValue":a[14]||(a[14]=l=>s(e).priority=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(n(!0),u(_,null,b(c.priorityOptions,l=>(n(),u("option",{key:l,value:l},v(l.charAt(0).toUpperCase()+l.slice(1)),9,oe))),128))],512),[[f,s(e).priority]]),o(d,{class:"mt-2",message:s(e).errors.priority},null,8,["message"])]),t("div",null,[o(r,{for:"score",value:"Score (0-100)"}),o(i,{id:"score",modelValue:s(e).score,"onUpdate:modelValue":a[15]||(a[15]=l=>s(e).score=l),type:"number",min:"0",max:"100",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.score},null,8,["message"])])])]),t("div",te,[ae,t("div",de,[t("div",null,[o(r,{for:"project_type",value:"Project Type"}),o(i,{id:"project_type",modelValue:s(e).project_type,"onUpdate:modelValue":a[16]||(a[16]=l=>s(e).project_type=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.project_type},null,8,["message"])]),t("div",null,[o(r,{for:"estimated_budget",value:"Estimated Budget ($)"}),o(i,{id:"estimated_budget",modelValue:s(e).estimated_budget,"onUpdate:modelValue":a[17]||(a[17]=l=>s(e).estimated_budget=l),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.estimated_budget},null,8,["message"])]),t("div",null,[o(r,{for:"budget_range",value:"Budget Range"}),p(t("select",{id:"budget_range","onUpdate:modelValue":a[18]||(a[18]=l=>s(e).budget_range=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[re,(n(!0),u(_,null,b(c.budgetRangeOptions,l=>(n(),u("option",{key:l,value:l},v(x(l)),9,ie))),128))],512),[[f,s(e).budget_range]]),o(d,{class:"mt-2",message:s(e).errors.budget_range},null,8,["message"])]),t("div",null,[o(r,{for:"assigned_to",value:"Assign To"}),p(t("select",{id:"assigned_to","onUpdate:modelValue":a[19]||(a[19]=l=>s(e).assigned_to=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[ne,(n(!0),u(_,null,b(c.users,l=>(n(),u("option",{key:l.id,value:l.id},v(l.name),9,ue))),128))],512),[[f,s(e).assigned_to]]),o(d,{class:"mt-2",message:s(e).errors.assigned_to},null,8,["message"])]),t("div",me,[o(r,{for:"requirements",value:"Requirements"}),p(t("textarea",{id:"requirements","onUpdate:modelValue":a[20]||(a[20]=l=>s(e).requirements=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Project requirements and details"},null,512),[[w,s(e).requirements]]),o(d,{class:"mt-2",message:s(e).errors.requirements},null,8,["message"])])])]),t("div",ce,[pe,t("div",ge,[t("div",null,[o(r,{for:"initial_conversation",value:"Initial Conversation"}),p(t("textarea",{id:"initial_conversation","onUpdate:modelValue":a[21]||(a[21]=l=>s(e).initial_conversation=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Record of initial conversation"},null,512),[[w,s(e).initial_conversation]]),o(d,{class:"mt-2",message:s(e).errors.initial_conversation},null,8,["message"])]),t("div",null,[o(r,{for:"notes",value:"Notes"}),p(t("textarea",{id:"notes","onUpdate:modelValue":a[22]||(a[22]=l=>s(e).notes=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Additional notes about the prospect"},null,512),[[w,s(e).notes]]),o(d,{class:"mt-2",message:s(e).errors.notes},null,8,["message"])]),t("div",null,[o(r,{for:"next_follow_up_at",value:"Next Follow-up Date"}),o(i,{id:"next_follow_up_at",modelValue:s(e).next_follow_up_at,"onUpdate:modelValue":a[23]||(a[23]=l=>s(e).next_follow_up_at=l),type:"datetime-local",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.next_follow_up_at},null,8,["message"])])])]),t("div",_e,[t("div",fe,[o(C,{href:g.route("prospects.index")},{svg:y(()=>[be]),_:1},8,["href"]),o(L,{disabled:s(e).processing},{default:y(()=>[A("Save")]),_:1},8,["disabled"])])])],40,O)])])]),_:1})],64))}},Se=q(ve,[["__scopeId","data-v-d84acd2a"]]);export{Se as default};
