import{r as U,T as k,o as i,c as d,a,u as n,w as u,F as S,Z as z,b as t,t as l,f as c,d as A,g as p,n as j,h as V,k as h,v as M,y as N}from"./app-53ab0fb8.js";import{_ as Q,b as D}from"./AdminLayout-964868e9.js";import{P as C}from"./PrimaryButton-170c8c15.js";import{_ as v}from"./SecondaryButton-11a8311f.js";import{M as $}from"./Modal-ba68642b.js";import{_ as f}from"./InputLabel-686b32c8.js";import{_ as F}from"./TextInput-c743e100.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const Z={class:"animate-top"},G={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},H={class:"text-3xl font-bold text-gray-900"},J=t("p",{class:"text-gray-600 mt-1"},"Prospect Details",-1),K={class:"flex space-x-4"},X=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),Y={class:"grid grid-cols-1 lg:grid-cols-9 gap-6"},tt={class:"lg:col-span-6 space-y-6"},et={class:"bg-white shadow rounded-lg p-6"},st=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),ot={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),at={class:"mt-1 text-sm text-gray-700"},it=t("label",{class:"block text-sm font-semibold text-gray-900"},"Email",-1),dt={class:"mt-1 text-sm text-gray-700"},nt={key:0},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Phone",-1),rt={class:"mt-1 text-sm text-gray-700"},ut={key:1},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Company",-1),pt={class:"mt-1 text-sm text-gray-700"},ft={key:2},bt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Position",-1),xt={class:"mt-1 text-sm text-gray-700"},_t={key:3},gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Location",-1),yt={class:"mt-1 text-sm text-gray-700"},ht={class:"bg-white shadow rounded-lg p-6"},vt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Activity Timeline",-1),wt={key:0,class:"space-y-4"},kt={class:"flex-shrink-0"},St={class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},Ct={class:"text-indigo-600 text-xs font-medium"},Ut={class:"flex-1 min-w-0"},At={class:"flex items-center justify-between"},Vt={class:"text-sm font-semibold text-gray-900"},Nt={key:0,class:"mt-1 text-sm text-gray-500"},$t={class:"flex items-center justify-between"},Ft={key:0,class:"mt-1 text-xs text-gray-600"},Pt={class:"text-xs text-gray-600"},jt={key:1,class:"text-center py-8 text-gray-500"},Mt={class:"lg:col-span-3 space-y-6"},Dt={class:"bg-white shadow rounded-lg p-6"},qt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),Lt={class:"flex flex-wrap gap-3"},Bt={key:0,class:"bg-white shadow rounded-lg p-6"},Tt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Notes & Conversation",-1),It={class:"space-y-4"},Et={key:0},Ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Initial Conversation:",-1),Rt={class:"mt-1 text-sm text-gray-700"},Wt={key:1},zt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Requirements:",-1),Qt={class:"mt-1 text-sm text-gray-700"},Zt={key:2},Gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Notes:",-1),Ht={class:"mt-1 text-sm text-gray-700"},Jt={key:1,class:"bg-white shadow rounded-lg p-6"},Kt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"URLs",-1),Xt={class:"flex flex-col gap-1"},Yt=["href"],te=["href"],ee=["href"],se={class:"bg-white shadow rounded-lg p-6"},oe=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Status & Priority",-1),le={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ae={class:""},ie=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status:",-1),de={class:""},ne=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority:",-1),ce=t("label",{class:"block text-sm font-semibold text-gray-900"},"Score:",-1),re={class:"mt-1 text-sm text-gray-700"},ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Source:",-1),me={class:"mt-1 text-sm text-gray-700"},pe={key:0},fe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To:",-1),be={class:"mt-1 text-sm text-gray-700"},xe={class:"bg-white shadow rounded-lg p-6"},_e=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Project Information",-1),ge={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ye={key:0},he=t("label",{class:"block text-sm font-semibold text-gray-900"},"Project Type:",-1),ve={class:"mt-1 text-sm text-gray-700"},we={key:1},ke=t("label",{class:"block text-sm font-semibold text-gray-900"},"Estimated Budget:",-1),Se={class:"mt-1 text-sm text-gray-700"},Ce={key:2},Ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Next Follow-up:",-1),Ae={class:"mt-1 text-sm text-gray-700"},Ve={key:3},Ne=t("label",{class:"block text-sm font-semibold text-gray-900"},"Converted Lead:",-1),$e={class:"p-6"},Fe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Add Activity",-1),Pe=["onSubmit"],je=["value"],Me={class:"flex justify-end space-x-3"},De={class:"p-6"},qe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Update Status",-1),Le=["onSubmit"],Be=["value"],Te={class:"flex justify-end space-x-3"},Ie={class:"p-6"},Ee=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule Follow-up",-1),Oe=["onSubmit"],Re={class:"flex justify-end space-x-3"},Ye={__name:"Show",props:{prospect:Object},setup(e){const w=e,_=U(!1),g=U(!1),y=U(!1),m=k({activity_type:"note_added",title:"",description:"",activity_date:new Date().toISOString().slice(0,16)}),b=k({status:w.prospect.status,notes:""}),x=k({next_follow_up_at:"",notes:""});k({});const q=()=>{m.post(route("prospects.addActivity",w.prospect.id),{onSuccess:()=>{_.value=!1,m.reset()}})},L=()=>{b.patch(route("prospects.updateStatus",w.prospect.id),{onSuccess:()=>{g.value=!1,b.reset()}})},B=()=>{x.post(route("prospects.scheduleFollowUp",w.prospect.id),{onSuccess:()=>{y.value=!1,x.reset()}})},T=r=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-gray-100 text-gray-800"})[r]||"bg-gray-100 text-gray-800",I=r=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800",P=r=>r?new Date(r).toLocaleString():"-",E=r=>r?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r):"-",O=r=>r.charAt(0).toUpperCase()+r.slice(1).replace("_"," "),R=[{value:"email_sent",label:"Email Sent"},{value:"email_received",label:"Email Received"},{value:"call_made",label:"Call Made"},{value:"call_received",label:"Call Received"},{value:"meeting_scheduled",label:"Meeting Scheduled"},{value:"meeting_completed",label:"Meeting Completed"},{value:"note_added",label:"Note Added"},{value:"linkedin_message",label:"LinkedIn Message"},{value:"proposal_sent",label:"Proposal Sent"},{value:"follow_up_scheduled",label:"Follow-up Scheduled"},{value:"document_shared",label:"Document Shared"},{value:"other",label:"Other"}],W=["new","contacted","qualified","unqualified","converted","lost"];return(r,o)=>(i(),d(S,null,[a(n(z),{title:`Prospect: ${e.prospect.first_name} ${e.prospect.last_name}`},null,8,["title"]),a(Q,null,{default:u(()=>[t("div",Z,[t("div",G,[t("div",null,[t("h1",H,"Prospect: "+l(e.prospect.first_name)+" "+l(e.prospect.last_name),1),J]),t("div",K,[a(D,{href:r.route("prospects.index")},{svg:u(()=>[X]),_:1},8,["href"])])]),t("div",Y,[t("div",tt,[t("div",et,[st,t("div",ot,[t("div",null,[lt,t("p",at,l(e.prospect.first_name)+" "+l(e.prospect.last_name),1)]),t("div",null,[it,t("p",dt,l(e.prospect.email||"N/A"),1)]),e.prospect.phone?(i(),d("div",nt,[ct,t("p",rt,l(e.prospect.phone),1)])):c("",!0),e.prospect.company?(i(),d("div",ut,[mt,t("p",pt,l(e.prospect.company),1)])):c("",!0),e.prospect.position?(i(),d("div",ft,[bt,t("p",xt,l(e.prospect.position??"N/A"),1)])):c("",!0),e.prospect.country||e.prospect.city?(i(),d("div",_t,[gt,t("p",yt,l(e.prospect.city)+l(e.prospect.city&&e.prospect.country?", ":"")+l(e.prospect.country),1)])):c("",!0)])]),t("div",ht,[vt,e.prospect.activities&&e.prospect.activities.length>0?(i(),d("div",wt,[(i(!0),d(S,null,A(e.prospect.activities,s=>(i(),d("div",{key:s.id,class:"flex items-start space-x-3 p-4 bg-gray-50 rounded-lg"},[t("div",kt,[t("div",St,[t("span",Ct,l(s.activity_type.charAt(0).toUpperCase()),1)])]),t("div",Ut,[t("div",At,[t("p",Vt,l(s.title),1)]),s.description?(i(),d("p",Nt,l(s.description),1)):c("",!0),t("div",$t,[s.user?(i(),d("p",Ft,"by "+l(s.user.first_name),1)):c("",!0),t("p",Pt,l(P(s.activity_date)),1)])])]))),128))])):(i(),d("div",jt," No activities recorded yet. "))])]),t("div",Mt,[t("div",Dt,[qt,t("div",Lt,[a(C,{onClick:o[0]||(o[0]=s=>_.value=!0)},{default:u(()=>[p(" Add Activity ")]),_:1}),a(v,{class:"w-full",onClick:o[1]||(o[1]=s=>g.value=!0)},{default:u(()=>[p(" Update Status ")]),_:1}),a(v,{class:"w-full",onClick:o[2]||(o[2]=s=>y.value=!0)},{default:u(()=>[p(" Schedule Follow-up ")]),_:1})])]),e.prospect.initial_conversation||e.prospect.notes||e.prospect.requirements?(i(),d("div",Bt,[Tt,t("div",It,[e.prospect.initial_conversation?(i(),d("div",Et,[Ot,t("p",Rt,l(e.prospect.initial_conversation),1)])):c("",!0),e.prospect.requirements?(i(),d("div",Wt,[zt,t("p",Qt,l(e.prospect.requirements),1)])):c("",!0),e.prospect.notes?(i(),d("div",Zt,[Gt,t("p",Ht,l(e.prospect.notes),1)])):c("",!0)])])):c("",!0),e.prospect.linkedin_url||e.prospect.website_url||e.prospect.company_website?(i(),d("div",Jt,[Kt,t("div",Xt,[e.prospect.linkedin_url?(i(),d("a",{key:0,href:e.prospect.linkedin_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," LinkedIn Profile ",8,Yt)):c("",!0),e.prospect.website_url?(i(),d("a",{key:1,href:e.prospect.website_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Personal Website ",8,te)):c("",!0),e.prospect.company_website?(i(),d("a",{key:2,href:e.prospect.company_website,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Company Website ",8,ee)):c("",!0)])])):c("",!0),t("div",se,[oe,t("div",le,[t("div",ae,[ie,t("span",{class:j([T(e.prospect.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.status.charAt(0).toUpperCase()+e.prospect.status.slice(1)),3)]),t("div",de,[ne,t("span",{class:j([I(e.prospect.priority),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.priority.charAt(0).toUpperCase()+e.prospect.priority.slice(1)),3)]),t("div",null,[ce,t("span",re,l(e.prospect.score)+"/100",1)]),t("div",null,[ue,t("span",me,l(O(e.prospect.lead_source)),1)]),e.prospect.assigned_user?(i(),d("div",pe,[fe,t("span",be,l(e.prospect.assigned_user.name),1)])):c("",!0)])]),t("div",xe,[_e,t("div",ge,[e.prospect.project_type?(i(),d("div",ye,[he,t("p",ve,l(e.prospect.project_type),1)])):c("",!0),e.prospect.estimated_budget?(i(),d("div",we,[ke,t("p",Se,l(E(e.prospect.estimated_budget)),1)])):c("",!0),e.prospect.next_follow_up_at?(i(),d("div",Ce,[Ue,t("p",Ae,l(P(e.prospect.next_follow_up_at)),1)])):c("",!0),e.prospect.converted_lead?(i(),d("div",Ve,[Ne,a(D,{href:r.route("leads.show",e.prospect.converted_lead.id),class:"ml-2 text-sm text-indigo-600"},{default:u(()=>[p(" View Lead #"+l(e.prospect.converted_lead.id),1)]),_:1},8,["href"])])):c("",!0)])])])])]),a($,{show:_.value,onClose:o[8]||(o[8]=s=>_.value=!1)},{default:u(()=>[t("div",$e,[Fe,t("form",{onSubmit:V(q,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"activity_type",value:"Activity Type"}),h(t("select",{id:"activity_type","onUpdate:modelValue":o[3]||(o[3]=s=>n(m).activity_type=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(R,s=>t("option",{key:s.value,value:s.value},l(s.label),9,je)),64))],512),[[M,n(m).activity_type]])]),t("div",null,[a(f,{for:"activity_title",value:"Title"}),a(F,{id:"activity_title",modelValue:n(m).title,"onUpdate:modelValue":o[4]||(o[4]=s=>n(m).title=s),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[a(f,{for:"activity_description",value:"Description"}),h(t("textarea",{id:"activity_description","onUpdate:modelValue":o[5]||(o[5]=s=>n(m).description=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[N,n(m).description]])]),t("div",null,[a(f,{for:"activity_date",value:"Activity Date"}),a(F,{id:"activity_date",modelValue:n(m).activity_date,"onUpdate:modelValue":o[6]||(o[6]=s=>n(m).activity_date=s),type:"datetime-local",class:"mt-1 block w-full"},null,8,["modelValue"])]),t("div",Me,[a(v,{onClick:o[7]||(o[7]=s=>_.value=!1)},{default:u(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(m).processing},{default:u(()=>[p("Add Activity")]),_:1},8,["disabled"])])],40,Pe)])]),_:1},8,["show"]),a($,{show:g.value,onClose:o[12]||(o[12]=s=>g.value=!1)},{default:u(()=>[t("div",De,[qe,t("form",{onSubmit:V(L,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"status",value:"Status"}),h(t("select",{id:"status","onUpdate:modelValue":o[9]||(o[9]=s=>n(b).status=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(W,s=>t("option",{key:s,value:s},l(s.charAt(0).toUpperCase()+s.slice(1)),9,Be)),64))],512),[[M,n(b).status]])]),t("div",null,[a(f,{for:"status_notes",value:"Notes"}),h(t("textarea",{id:"status_notes","onUpdate:modelValue":o[10]||(o[10]=s=>n(b).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Optional notes about the status change"},null,512),[[N,n(b).notes]])]),t("div",Te,[a(v,{onClick:o[11]||(o[11]=s=>g.value=!1)},{default:u(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(b).processing},{default:u(()=>[p("Update Status")]),_:1},8,["disabled"])])],40,Le)])]),_:1},8,["show"]),a($,{show:y.value,onClose:o[16]||(o[16]=s=>y.value=!1)},{default:u(()=>[t("div",Ie,[Ee,t("form",{onSubmit:V(B,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"follow_up_date",value:"Follow-up Date"}),a(F,{id:"follow_up_date",modelValue:n(x).next_follow_up_at,"onUpdate:modelValue":o[13]||(o[13]=s=>n(x).next_follow_up_at=s),type:"datetime-local",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[a(f,{for:"follow_up_notes",value:"Notes"}),h(t("textarea",{id:"follow_up_notes","onUpdate:modelValue":o[14]||(o[14]=s=>n(x).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Notes about the follow-up"},null,512),[[N,n(x).notes]])]),t("div",Re,[a(v,{onClick:o[15]||(o[15]=s=>y.value=!1)},{default:u(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(x).processing},{default:u(()=>[p("Schedule Follow-up")]),_:1},8,["disabled"])])],40,Oe)])]),_:1},8,["show"])]),_:1})],64))}};export{Ye as default};
