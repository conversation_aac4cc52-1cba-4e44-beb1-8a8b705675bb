import{o as n,c as l,a,u as e,w as u,F as y,Z as v,b as r,h as _,t as d,f as m,g as x,A as g}from"./app-53ab0fb8.js";import{_ as h,b as V}from"./AdminLayout-964868e9.js";/* empty css                                                              */import{_ as i}from"./InputLabel-686b32c8.js";import{_ as p}from"./TextInput-c743e100.js";import{P as b}from"./PrimaryButton-170c8c15.js";import{u as w}from"./index-1a0c4841.js";import{Q as k}from"./vue-quill.snow-49c9ac7c.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top"},$={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},S=r("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create SMTP",-1),U=["onSubmit"],I={class:"border-b border-gray-900/10 pb-12"},P={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},E={class:"sm:col-span-3"},N={key:0,class:"mt-1 text-sm text-red-500"},T={class:"sm:col-span-3"},B={key:0,class:"mt-1 text-sm text-red-500"},F={class:"sm:col-span-3"},M={key:0,class:"mt-1 text-sm text-red-500"},j={class:"sm:col-span-3"},A={key:0,class:"mt-1 text-sm text-red-500"},Q={class:"sm:col-span-3"},D={key:0,class:"mt-1 text-sm text-red-500"},H={class:"sm:col-span-3"},O={key:0,class:"mt-1 text-sm text-red-500"},Z={class:"sm:col-span-3"},q={key:0,class:"mt-1 text-sm text-red-500"},z={class:"sm:col-span-6"},G={key:0,class:"mt-1 text-sm text-red-500"},J={class:"flex mt-6 items-center justify-between"},K={class:"ml-auto flex items-center justify-end gap-x-6 mt-12"},L=r("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),R={key:0,class:"text-sm text-gray-600"},le={__name:"Add",setup(W){const t=w("post","smtp",{host:"",port:"",username:"",password:"",email:"",encryption:"",name:"",companyInfo:""}),c=()=>t.submit({preserveScroll:!0,resetOnSuccess:!1});return(f,o)=>(n(),l(y,null,[a(e(v),{title:"SMTP"}),a(h,null,{default:u(()=>[r("div",C,[r("div",$,[S,r("form",{onSubmit:_(c,["prevent"]),class:""},[r("div",I,[r("div",P,[r("div",E,[a(i,{for:"host",value:"Host"}),a(p,{id:"host",type:"text",modelValue:e(t).host,"onUpdate:modelValue":o[0]||(o[0]=s=>e(t).host=s),autocomplete:"host",onChange:o[1]||(o[1]=s=>e(t).validate("host"))},null,8,["modelValue"]),e(t).errors.host?(n(),l("p",N,d(e(t).errors.host),1)):m("",!0)]),r("div",T,[a(i,{for:"port",value:"Port"}),a(p,{id:"port",type:"text",modelValue:e(t).port,"onUpdate:modelValue":o[2]||(o[2]=s=>e(t).port=s),autocomplete:"port",onChange:o[3]||(o[3]=s=>e(t).validate("port"))},null,8,["modelValue"]),e(t).errors.port?(n(),l("p",B,d(e(t).errors.port),1)):m("",!0)]),r("div",F,[a(i,{for:"username",value:"Username"}),a(p,{id:"username",type:"text",modelValue:e(t).username,"onUpdate:modelValue":o[4]||(o[4]=s=>e(t).username=s),autocomplete:"username",onChange:o[5]||(o[5]=s=>e(t).validate("username"))},null,8,["modelValue"]),e(t).errors.username?(n(),l("p",M,d(e(t).errors.username),1)):m("",!0)]),r("div",j,[a(i,{for:"password",value:"Password"}),a(p,{id:"password",type:"text",modelValue:e(t).password,"onUpdate:modelValue":o[6]||(o[6]=s=>e(t).password=s),autocomplete:"password",onChange:o[7]||(o[7]=s=>e(t).validate("password"))},null,8,["modelValue"]),e(t).errors.password?(n(),l("p",A,d(e(t).errors.password),1)):m("",!0)]),r("div",Q,[a(i,{for:"email",value:"Email"}),a(p,{id:"email",type:"text",modelValue:e(t).email,"onUpdate:modelValue":o[8]||(o[8]=s=>e(t).email=s),autocomplete:"email",onChange:o[9]||(o[9]=s=>e(t).validate("email"))},null,8,["modelValue"]),e(t).errors.email?(n(),l("p",D,d(e(t).errors.email),1)):m("",!0)]),r("div",H,[a(i,{for:"encryption",value:"Encryption"}),a(p,{id:"encryption",type:"text",modelValue:e(t).encryption,"onUpdate:modelValue":o[10]||(o[10]=s=>e(t).encryption=s),autocomplete:"encryption",onChange:o[11]||(o[11]=s=>e(t).validate("encryption"))},null,8,["modelValue"]),e(t).errors.encryption?(n(),l("p",O,d(e(t).errors.encryption),1)):m("",!0)]),r("div",Z,[a(i,{for:"name",value:"Name"}),a(p,{id:"name",type:"text",modelValue:e(t).name,"onUpdate:modelValue":o[12]||(o[12]=s=>e(t).name=s),autocomplete:"name",onChange:o[13]||(o[13]=s=>e(t).validate("name"))},null,8,["modelValue"]),e(t).errors.name?(n(),l("p",q,d(e(t).errors.name),1)):m("",!0)]),r("div",z,[a(i,{value:"Company Info"}),a(e(k),{content:e(t).companyInfo,"onUpdate:content":o[14]||(o[14]=s=>e(t).companyInfo=s),contentType:"html",theme:"snow",toolbar:"full",options:{modules:{toolbar:[["bold","italic","underline"],["link"],[{list:"ordered"},{list:"bullet"}],["clean"]]},placeholder:"Enter company info"},class:"h-64 mb-8"},null,8,["content"]),e(t).errors.companyInfo?(n(),l("p",G,d(e(t).errors.companyInfo),1)):m("",!0)])])]),r("div",J,[r("div",K,[a(V,{href:f.route("smtp.index")},{svg:u(()=>[L]),_:1},8,["href"]),a(b,{disabled:e(t).processing},{default:u(()=>[x("Save")]),_:1},8,["disabled"]),a(g,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[e(t).recentlySuccessful?(n(),l("p",R,"Saved.")):m("",!0)]),_:1})])])],40,U)])])]),_:1})],64))}};export{le as default};
